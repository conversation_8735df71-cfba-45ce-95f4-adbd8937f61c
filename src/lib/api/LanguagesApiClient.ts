import type {BaseApiResponse} from '$lib/api/base/BaseApiClient';
import MainApiClient from '$lib/api/base/MainApiClient';
import type Language from '$lib/domain/Language';

export default class LanguagesApiClient extends MainApiClient {
    public async index(): Promise<IndexResponse> {
        return await this.get('/languages');
    }
}

interface IndexResponse extends BaseApiResponse {
    data: Language[];
}
