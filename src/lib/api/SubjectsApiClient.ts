import type {BaseApiResponse} from '$lib/api/base/BaseApiClient';
import MainApiClient from '$lib/api/base/MainApiClient';
import type Subject from '$lib/domain/Subject';

export default class SubjectsApiClient extends MainApiClient {
    public async index(query: string): Promise<IndexResponse> {
        return await this.get('/subjects', {query});
    }
}

interface IndexResponse extends BaseApiResponse {
    data: Subject[];
}
