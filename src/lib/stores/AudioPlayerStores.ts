import {type Writable, writable} from 'svelte/store';

type AudioPlayerStores = {
    isOpen: Writable<boolean>;
    isPlaying: Writable<boolean>;
    chapterIndex: Writable<number | null>;
    playlist: Writable<{
        name: string;
        url: string;
    }[]>;
};

const stores: AudioPlayerStores = {
    isOpen: writable(false),
    isPlaying: writable(false),
    chapterIndex: writable(null),
    playlist: writable([]),
};

export default stores;
