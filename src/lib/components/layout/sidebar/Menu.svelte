<script lang="ts">
    import Logo from '$lib/components/layout/header/Logo.svelte';
    import MenuItem from '$lib/components/layout/sidebar/MenuItem.svelte';
    import BookOpenSvg from '$lib/components/svg/BookOpenSvg.svelte';
    import ChatBubbleSvg from "$lib/components/svg/ChatBubbleSvg.svelte";
    import HomeSvg from '$lib/components/svg/HomeSvg.svelte';
    import SearchSvg from '$lib/components/svg/SearchSvg.svelte';
    import {t} from '$lib/localization/Localization';
    import AppRoutes from '$routes/AppRoutes';
</script>

<div class="mb-5 sm:hidden">
    <Logo />
</div>
<ul class="space-y-2">
    <MenuItem title={$t('menu.home')} url={AppRoutes.home} iconComponent={HomeSvg} />
    <MenuItem title={$t('menu.search')} url={AppRoutes.search} iconComponent={SearchSvg} />
    <MenuItem title={$t('menu.recommendations')} url={AppRoutes.recommendations} iconComponent={ChatBubbleSvg} />
    <MenuItem title={$t('menu.myLibrary')} url={AppRoutes.myLibrary} iconComponent={BookOpenSvg} />
</ul>
