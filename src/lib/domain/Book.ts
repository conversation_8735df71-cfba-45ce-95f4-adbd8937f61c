import type BookIdentifier from '$lib/domain/BookIdentifier';
import type BookReadStatusType from '$lib/domain/BookReadStatusType';
import type Language from '$lib/domain/Language';
import type ReadingActivity from '$lib/domain/ReadingActivity';

type Book = {
    authorIDs: string[];
    authors: string[];
    cover: string | null;
    description: string | null;
    extraIdentifiers: BookIdentifier[];
    identifiers: Record<string, string>;
    isFree: boolean;
    isInLists: Record<string, boolean>;
    isLiked: number;
    languages: Language[];
    maturityRating: string | null;
    numberOfPages: number | null;
    publishDates: number[];
    publisher: string | null;
    readingActivity: ReadingActivity[];
    readStatus: BookReadStatusType | null;
    subjects: string[];
    subtitle: string | null;
    title: string;
    urlIdentifier: {key: string; type: string};
    uuid: string;
};

export default Book;
